# ATAS Management Script
# Simple commands to manage Rithmic in ATAS-only mode

param(
    [ValidateSet("start", "stop", "status", "restart")]
    [string]$Action = "status"
)

function Start-ATAS {
    Write-Host "Starting Rithmic for ATAS..." -ForegroundColor Green
    
    # Set ATAS-only environment
    $env:RITHMIC_MODE = "ATAS_ONLY"
    $env:DISABLE_UI = "1"
    $env:ATAS_ONLY = "1"
    
    Start-Process -FilePath ".\RithmicX64.exe" -ArgumentList "-atas" -WindowStyle Hidden
    Start-Sleep -Seconds 3
    
    Show-Status
}

function Stop-ATAS {
    Write-Host "Stopping Rithmic processes..." -ForegroundColor Yellow
    Get-Process | Where-Object {$_.ProcessName -like "*Rithmic*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "Stopped." -ForegroundColor Green
}

function Show-Status {
    $processes = Get-Process | Where-Object {$_.ProcessName -like "*Rithmic*"}
    if ($processes) {
        Write-Host "Rithmic Status: RUNNING (ATAS-only mode)" -ForegroundColor Green
        $processes | Format-Table ProcessName, Id, CPU -AutoSize
    } else {
        Write-Host "Rithmic Status: STOPPED" -ForegroundColor Red
    }
}

function Restart-ATAS {
    Stop-ATAS
    Start-Sleep -Seconds 2
    Start-ATAS
}

# Execute action
switch ($Action) {
    "start" { Start-ATAS }
    "stop" { Stop-ATAS }
    "status" { Show-Status }
    "restart" { Restart-ATAS }
}

Write-Host "`nUsage examples:" -ForegroundColor Cyan
Write-Host "  .\manage_atas.ps1 start    # Start ATAS mode" -ForegroundColor White
Write-Host "  .\manage_atas.ps1 stop     # Stop Rithmic" -ForegroundColor White
Write-Host "  .\manage_atas.ps1 status   # Check status" -ForegroundColor White
Write-Host "  .\manage_atas.ps1 restart  # Restart in ATAS mode" -ForegroundColor White
