# Complete ATAS-Only Solution for RithmicX64
# This script ensures Rithmic only serves ATAS functionality

Write-Host "=== Rithmic ATAS-Only Solution ===" -ForegroundColor Green

# Function to stop all Rithmic processes
function Stop-RithmicProcesses {
    Write-Host "Stopping existing Rithmic processes..." -ForegroundColor Yellow
    Get-Process | Where-Object {$_.ProcessName -like "*Rithmic*"} | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
}

# Function to start ATAS-only mode
function Start-ATASMode {
    Write-Host "Starting Rithmic in ATAS-only mode..." -ForegroundColor Cyan
    
    # Set restrictive environment variables
    $env:RITHMIC_MODE = "ATAS_ONLY"
    $env:DISABLE_UI = "1"
    $env:DISABLE_CHARTS = "1"
    $env:DISABLE_NEWS = "1"
    $env:DISABLE_REPORTS = "1"
    $env:MINIMAL_MODE = "1"
    $env:ATAS_ONLY = "1"
    
    # Try different startup methods
    $methods = @(
        @{Args = @("-atas"); Description = "ATAS flag"},
        @{Args = @("-minimal", "-atas"); Description = "Minimal + ATAS"},
        @{Args = @("-service"); Description = "Service mode"},
        @{Args = @(); Description = "Default with env vars"}
    )
    
    foreach ($method in $methods) {
        Write-Host "Trying: $($method.Description)" -ForegroundColor White
        try {
            if ($method.Args.Count -gt 0) {
                Start-Process -FilePath ".\RithmicX64.exe" -ArgumentList $method.Args -WindowStyle Hidden
            } else {
                Start-Process -FilePath ".\RithmicX64.exe" -WindowStyle Hidden
            }
            
            Start-Sleep -Seconds 3
            $processes = Get-Process | Where-Object {$_.ProcessName -like "*Rithmic*"}
            if ($processes) {
                Write-Host "Success! Rithmic started with: $($method.Description)" -ForegroundColor Green
                return $true
            }
        } catch {
            Write-Host "Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    return $false
}

# Function to verify ATAS connection
function Test-ATASConnection {
    Write-Host "`nVerifying ATAS connection..." -ForegroundColor Cyan
    
    # Check if Rithmic processes are running
    $processes = Get-Process | Where-Object {$_.ProcessName -like "*Rithmic*"}
    if ($processes) {
        Write-Host "Rithmic processes found:" -ForegroundColor Green
        $processes | Format-Table ProcessName, Id, CPU -AutoSize
        
        Write-Host "`nATAS Connection Status:" -ForegroundColor Yellow
        Write-Host "1. Check ATAS data connection indicator" -ForegroundColor White
        Write-Host "2. Verify market data is flowing" -ForegroundColor White
        Write-Host "3. Test order routing (if applicable)" -ForegroundColor White
        
        return $true
    } else {
        Write-Host "No Rithmic processes running!" -ForegroundColor Red
        return $false
    }
}

# Main execution
Write-Host "Step 1: Cleaning up existing processes" -ForegroundColor Magenta
Stop-RithmicProcesses

Write-Host "`nStep 2: Starting ATAS-only mode" -ForegroundColor Magenta
$success = Start-ATASMode

if ($success) {
    Write-Host "`nStep 3: Verifying connection" -ForegroundColor Magenta
    Test-ATASConnection
    
    Write-Host "`n=== SUCCESS ===" -ForegroundColor Green
    Write-Host "Rithmic is now running in ATAS-only mode" -ForegroundColor Yellow
    Write-Host "Only ATAS functionality is available" -ForegroundColor Yellow
    Write-Host "`nTo stop: Run 'Get-Process *Rithmic* | Stop-Process'" -ForegroundColor Cyan
} else {
    Write-Host "`n=== FAILED ===" -ForegroundColor Red
    Write-Host "Could not start Rithmic in ATAS-only mode" -ForegroundColor Yellow
    Write-Host "Try running RithmicX64.exe manually" -ForegroundColor Cyan
}
