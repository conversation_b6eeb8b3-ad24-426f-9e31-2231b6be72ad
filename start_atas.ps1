# ATAS Only Mode Launcher
Write-Host "Starting Rithmic in ATAS-only mode..." -ForegroundColor Green

# Set environment variables to limit functionality
$env:RITHMIC_MODE = "ATAS_ONLY"
$env:DISABLE_UI = "1"
$env:MINIMAL_MODE = "1"

# Start the program
try {
    Start-Process -FilePath ".\RithmicX64.exe" -WindowStyle Minimized
    Write-Host "Rithmic started successfully - ATAS mode only" -ForegroundColor Yellow
    Write-Host "Check ATAS for data connection" -ForegroundColor Cyan
} catch {
    Write-Host "Failed to start: $($_.Exception.Message)" -ForegroundColor Red
}

# Check if process is running
Start-Sleep -Seconds 3
$processes = Get-Process | Where-Object {$_.ProcessName -like "*Rithmic*"}
if ($processes) {
    Write-Host "Rithmic processes running:" -ForegroundColor Green
    $processes | Format-Table ProcessName, Id -AutoSize
} else {
    Write-Host "No Rithmic processes found" -ForegroundColor Red
}
